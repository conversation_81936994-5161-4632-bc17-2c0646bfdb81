using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Core.Utilities.Security.CompanyContext
{
    public class CompanyContext : ICompanyContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<CompanyContext> _logger;

        public CompanyContext(IHttpContextAccessor httpContextAccessor, ILogger<CompanyContext> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public int GetCompanyId()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                {
                    _logger.LogDebug("User not authenticated, returning -1 for company ID");
                    return -1;
                }

                var companyIdClaim = httpContext.User.Claims?.FirstOrDefault(c => c.Type == "CompanyId");

                if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int companyId) && companyId > 0)
                {
                    _logger.LogDebug("Found valid company ID in claims: {CompanyId}", companyId);
                    return companyId;
                }

                // Debug: Tüm claim'leri logla
                var allClaims = httpContext.User.Claims?.Select(c => $"{c.Type}={c.Value}").ToList();
                _logger.LogWarning("CompanyId claim not found or invalid. Available claims: {Claims}",
                    string.Join(", ", allClaims ?? new List<string>()));

                // Eğer claim bulunamazsa veya geçerli bir değer değilse, varsayılan bir değer döndür
                // Bu durumda -1 döndürüyoruz, bu da geçersiz bir CompanyID olduğunu gösterir
                return -1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting company ID from claims");
                return -1;
            }
        }
    }
}
